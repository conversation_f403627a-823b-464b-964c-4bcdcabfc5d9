package db

import (
	"fmt"
	"os"
	"path/filepath"
	"testing"

	ginkgo2 "github.com/onsi/ginkgo/v2"
	Ω "github.com/onsi/gomega"
	dockertest "github.com/ory/dockertest/v3"
	"github.com/ory/dockertest/v3/docker"
)

const (
	dbName   = "test"
	dbPwd    = "test"
	DB_GROUP = "cmb-uims"
)

func TestMysql(t *testing.T) {
	Ω.RegisterFailHandler(ginkgo2.Fail)
	ginkgo2.RunSpecs(t, "mysql test")
}

var (
	Db            *Conn
	dbPort        string
	cleanupDocker func()
	dialect       = DEFAULT_DB_DIALECT
	// dialect = DialectTypeDm // dm单元测试开启
)

// BeforeSuite 会在所有测试用例执行前执行
var _ = ginkgo2.BeforeSuite(func() {
	Db, cleanupDocker = setupGormWithDocker()
})

// AfterSuite 会在所有测试用例执行后执行
var _ = ginkgo2.AfterSuite(func() {
	cleanupDocker()
	cleanConfig()
})

var _ = ginkgo2.BeforeEach(func() {
	// clear db tables before each test
	Ω.Ω(cleanDatabase(Db, dbName)).To(Ω.Succeed())
})

func setupGormWithDocker() (*Conn, func()) {
	pool, err := dockertest.NewPool("")
	chk(err)
	runDockerOpt := &dockertest.RunOptions{
		Repository: "hub.deepin.com/library/mariadb",
		Tag:        "10.3.35",
		Env:        []string{"MARIADB_DATABASE=" + dbName, "MARIADB_ROOT_PASSWORD=" + dbPwd},
	}
	fnConfig := func(config *docker.HostConfig) {
		config.AutoRemove = true                     // set AutoRemove to true so that stopped container goes away by itself
		config.RestartPolicy = docker.NeverRestart() // don't restart container
	}
	resource, err := pool.RunWithOptions(runDockerOpt, fnConfig)
	chk(err)
	// call clean up function to release resource
	fnCleanup := func() {
		err := resource.Close()
		chk(err)
	}
	dbPort = resource.GetPort("3306/tcp") // 获取宿主机上的映射端口
	conStr := fmt.Sprintf("root:%s@tcp(127.0.0.1:%s)/%s?charset=utf8mb4&collation=utf8mb4_general_ci&parseTime=True&loc=Local&readTimeout=10s&timeout=10s&writeTimeout=10s",
		dbPwd,
		dbPort, // 获取宿主机上的映射端口
		dbName,
	)

	// fmt.Println(conStr)
	var gdb *Conn
	conf := Conf{
		DB_GROUP: GroupConf{
			Master: MasterConf{
				DSN:             conStr,
				Dialect:         dialect,
				LogMode:         true,
				LogSQL:          true,
				MaxOpenConns:    100,
				MaxIdleConns:    50,
				ConnMaxLifetime: 300,
				SlowThreshold:   1000,
			},
			Slave: SlaveConf{
				DSN:             []string{conStr},
				Dialect:         dialect,
				LogMode:         true,
				LogSQL:          true,
				MaxOpenConns:    100,
				MaxIdleConns:    50,
				ConnMaxLifetime: 300,
				SlowThreshold:   1000,
			},
		},
	}
	// retry until db server is ready
	err = pool.Retry(func() (err error) {
		defer func() {
			if r := recover(); r != nil {
				err = fmt.Errorf("gorm operation panicked: %v", r)
			}
		}()
		// 初始化数据库管理器
		InitManager(&conf)
		gdb = Master(DB_GROUP)
		return err
	})
	chk(err)
	return gdb, fnCleanup
}

// cleanDatabase 清空数据库中的所有表
func cleanDatabase(d *Conn, dbName string) error {

	err := d.Exec("SET FOREIGN_KEY_CHECKS = 0;").Error
	if err != nil {
		return err
	}
	var tables []string
	err = d.Raw("SHOW TABLES").Pluck("Tables_in_"+dbName, &tables).Error
	if err != nil {
		return err
	}
	for _, table := range tables {
		_ = d.Exec("DROP TABLE IF EXISTS " + table + ";").Error
	}
	return d.Exec("SET FOREIGN_KEY_CHECKS = 1;").Error
}

func chk(err error) {
	if err != nil {
		panic(err)
	}
}

func cleanConfig() {
	confDir := filepath.Join("./")
	configPath := filepath.Join(confDir, "config.toml")
	_ = os.RemoveAll(configPath)
}

// createMockSlaveConns 创建模拟的slave连接用于测试
func createMockSlaveConns(count int, activeStates []bool) []*Conn {
	slaves := make([]*Conn, count)
	for i := 0; i < count; i++ {
		// 创建基本的连接配置
		conf := &ConnConf{
			DSN:     fmt.Sprintf("mock-dsn-%d", i),
			Dialect: "mysql",
		}

		// 创建连接对象
		conn := &Conn{
			conf:     conf,
			isActive: activeStates[i],
		}

		// 如果连接是活跃的，设置一个有效的DB对象
		if activeStates[i] {
			// 使用测试数据库连接
			conn.DB = Db.DB
		}

		slaves[i] = conn
	}
	return slaves
}

// createTestGroup 创建测试用的Group
func createTestGroup(slaveCount int, activeStates []bool, policy GroupPolicy) *Group {
	group := &Group{
		Conn:   Db, // 使用测试套件中的主连接
		slaves: createMockSlaveConns(slaveCount, activeStates),
		policy: policy,
	}
	return group
}
