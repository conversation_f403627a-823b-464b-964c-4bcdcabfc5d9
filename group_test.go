package db

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

var dbGroup *Group

func TestNewGroup(t *testing.T) {
	dbGroupConf := GroupConf{
		Master: MasterConf{
			Dialect:         "mysql",
			DSN:             "root:db123456@tcp(127.0.0.1:3306)/db_test?parseTime=true&loc=Local&charset=utf8mb4,utf8",
			MaxOpenConns:    100,
			MaxIdleConns:    50,
			ConnMaxLifetime: 300,
			LogMode:         true,
		},
		Slave: SlaveConf{
			Dialect:         "mysql",
			DSN:             []string{"root:db123456@tcp(127.0.0.1:3306)/db_test?parseTime=true&loc=Local&charset=utf8mb4,utf8"},
			MaxOpenConns:    100,
			MaxIdleConns:    50,
			ConnMaxLifetime: 300,
			LogMode:         true,
		},
	}

	dg, err := NewGroup(&dbGroupConf)
	dbGroup = dg

	assert.Nil(t, err)
}

func TestPing(t *testing.T) {
	err := dbGroup.Ping()
	assert.Nil(t, err)
}

func TestClose(t *testing.T) {
	err := dbGroup.Close()
	assert.Nil(t, err)
}
