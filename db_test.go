package db

import (
	"fmt"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
)

var testDBMgr *Manager
var testDBConn *Conn

var testConf = Conf{
	"test": GroupConf{
		Master: MasterConf{
			Dialect:         "mysql",
			DSN:             "root:123456@tcp(172.17.0.2:3306)/syncdb?charset=utf8&parseTime=True&loc=Local&readTimeout=5s&timeout=5s&writeTimeout=3s",
			MaxOpenConns:    100,
			MaxIdleConns:    50,
			ConnMaxLifetime: 300,
			LogMode:         true,
			LogSQL:          true,
		},
		Slave: SlaveConf{
			Dialect:         "mysql",
			DSN:             []string{"root:123456@tcp(172.17.0.2:3306)/syncdb?charset=utf8&parseTime=True&loc=Local&readTimeout=5s&timeout=8s&writeTimeout=3s", "root:123456@tcp(172.17.0.3:3306)/syncdb?charset=utf8&parseTime=True&loc=Local&readTimeout=5s&timeout=8s&writeTimeout=3s", "root:123456@tcp(172.17.0.4:3306)/syncdb?charset=utf8&parseTime=True&loc=Local&readTimeout=5s&timeout=8s&writeTimeout=3s"},
			MaxOpenConns:    100,
			MaxIdleConns:    50,
			ConnMaxLifetime: 300,
			LogMode:         false,
			LogSQL:          false,
			//GroupPolicy:     "minConnects",
		},
	},
}

var testConnConf = ConnConf{
	Dialect:         "mysql",
	DSN:             "root:123456@tcp(172.17.0.2:3306)/syncdb?charset=utf8&parseTime=True&loc=Local&readTimeout=5s&timeout=5s&writeTimeout=3s",
	MaxOpenConns:    100,
	MaxIdleConns:    50,
	ConnMaxLifetime: 300,
	LogMode:         true,
	LogSQL:          true,
}

// User user struct
type User struct {
	ID   int
	Age  int
	Name string
}

// Product product struct
type Product struct {
	ID   int
	Name string
	User int
}

func TestNewManager(t *testing.T) {
	_, err := NewManager(&testConf)
	assert.Nil(t, err)

	_, err = NewManager(nil)
	assert.NotNil(t, err)
}

func TestManager_Master(t *testing.T) {
	mgr, err := NewManager(&testConf)
	assert.Nil(t, err)
	conn := mgr.Master("test")
	assert.NotNil(t, conn)
	assert.NotNil(t, conn.DB)

	conn = mgr.Master("invalid name")
	assert.NotNil(t, conn)
	assert.Nil(t, conn.DB)
}

func TestManager_Slave(t *testing.T) {
	mgr, err := NewManager(&testConf)
	assert.Nil(t, err)
	conn := mgr.Slave("test")
	assert.NotNil(t, conn)
	assert.NotNil(t, conn.DB)

	conn = mgr.Slave("invalid name")
	assert.NotNil(t, conn)
	assert.Nil(t, conn.DB)
}

func TestDefaultManager(t *testing.T) {
	InitManager(&testConf)
	mgr := DefaultManager()
	assert.NotNil(t, mgr)
	assert.NotNil(t, mgr.Master("test"))
}

func TestMaster(t *testing.T) {
	InitManager(&testConf)
	conn := Master("test")
	assert.NotNil(t, conn)
	assert.NotNil(t, conn.DB)

	conn = Master("invalid name")
	assert.NotNil(t, conn)
	assert.Nil(t, conn.DB)
}

func TestSlave(t *testing.T) {
	InitManager(&testConf)
	conn := Slave("test")
	assert.NotNil(t, conn)
	assert.NotNil(t, conn.DB)

	conn = Slave("invalid name")
	assert.NotNil(t, conn)
	assert.Nil(t, conn.DB)
}

// PcUser 终端允许用户登陆表
type PcUser struct {
	ID            int       `gorm:"type:int(11);AUTO_INCREMENT" json:"id"`
	PcID          int64     `gorm:"type:int(11)" json:"pc_id"`
	UserID        int       `gorm:"type:int(11)" json:"user_id"`
	CreatedAt     time.Time `gorm:"type:datetime" json:"created_at"`
	CreateAdminID int       `gorm:"type:smallint(5)" json:"create_admin_id" binding:"gte=1,lte=65535"`
	RuleType      int       `gorm:"type:tinyint(1)" json:"rule_type"`
}

// TableName 终端允许用户登陆表表名
func (pu *PcUser) TableName() string {
	return "pc_user"
}

func TestInitManager(t *testing.T) {
	InitManager(&testConf)
	for i := 0; i < 10000; i++ {
		user := PcUser{}
		if err := Slave("test").Model(&PcUser{}).Where("user_id = ?", i+1).First(&user).Error; err != nil {
			continue
		}
		//fmt.Println(i)
	}
	dsn, strings := GetSlaveDeadDSN("test")
	fmt.Println(dsn)
	fmt.Println(strings)
}
