
[DB]
    [DB.udcp-uim]                                       # DB组名称:test
        [DB.udcp-uim.Master]                            # Master配置
            dialect = "mysql"                       # DB类型:mysql
            dsn = "root:123456@tcp(172.17.0.2:3306)/syncdb?charset=utf8&parseTime=True&loc=Local&readTimeout=5s&timeout=5s&writeTimeout=3s"      # 地址
#             dialect = "dm"
#             dsn = "dm://udcp:123456789@10.20.23.192:5236/UIM?svcConfPath=./dm_uim.conf"
            maxOpenConns = 100                      # 最大连接数
            maxIdleConns = 50                       # 最大空闲连接数
            connMaxLifetime = 300                   # 最大空闲回收时间，单位：s
            logMode = false                          # 是否开启debug日志
            logSQL =false                            # 是否显示日志中的sql
            slowThreshold = 500                     # 慢日志阈值，单位：ms
        [DB.udcp-uim.Slave]                             # Slave配置
            dialect = "mysql"
            dsn = ["root:123456@tcp(172.17.0.2:3306)/syncdb?charset=utf8&parseTime=True&loc=Local&readTimeout=5s&timeout=8s&writeTimeout=3s","root:123456@tcp(172.17.0.3:3306)/syncdb?charset=utf8&parseTime=True&loc=Local&readTimeout=5s&timeout=8s&writeTimeout=3s","root:123456@tcp(172.17.0.4:3306)/syncdb?charset=utf8&parseTime=True&loc=Local&readTimeout=5s&timeout=8s&writeTimeout=3s"]
#             dialect = "dm"
#             dsn = ["dm://udcp:123456789@10.20.23.192:5236/UIM?svcConfPath=./dm_uim.conf", "dm://udcp:123456789@10.20.23.192:5236/UIM?svcConfPath=./dm_uim.conf"]
            maxOpenConns = 100
            maxIdleConns = 50
            connMaxLifetime = 300
            logMode = false
            logSQL =false
            slowThreshold = 500
            groupPolicy = "minConnects"
            checkPingTime = 5