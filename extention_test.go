package db

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestGetOne(t *testing.T) {
	var user User
	query := User{ID: 1}
	isExist, _ := testDBConn.GetOne(&user, query)
	t.Log("TestGetOne:", isExist)
	assert.True(t, isExist)
}

func TestGetList(t *testing.T) {
	var users []User
	err := testDBConn.GetList(&users, "id=?", 1002)
	t.Log("TestGetList:", users)
	assert.Nil(t, err)
}

func TestGetOrderedList(t *testing.T) {
	var users []User
	err := testDBConn.GetOrderedList(&users, "id desc", "")
	t.Log("TestGetOrderedList:", users)
	assert.Nil(t, err)
}

func TestGetFirstNRecords(t *testing.T) {
	var users []User
	err := testDBConn.GetOrderedList(&users, "id desc", 2, "")
	t.Log("TestGetFirstNRecords:", users)
	assert.Nil(t, err)
}

func TestGetPaginationList(t *testing.T) {
	var users []User
	err := testDBConn.GetPaginationList(&users, "id desc", 1, 2, "")
	t.Log("TestGetPaginationList:", users)
	assert.Nil(t, err)
}

func TestExecSQL(t *testing.T) {
	var users []User
	err := testDBConn.ExecSQL(&users, "select * from users")
	t.Log("TestExecSQL:", users)
	assert.Nil(t, err)
}
