package db

import (
	"pkg.deepin.com/service/lib/log"
	"time"
)

// CirculatePolicy 轮询方式
func CirculatePolicy() *CirculatePolicyHandler {
	return &CirculatePolicyHandler{}
}

// CirculatePolicyHandler 轮训方式
type CirculatePolicyHandler struct {
	cursor int // 游标
	num    int // 当前查询元素的个数
}

func (c *CirculatePolicyHandler) Slave(group *Group) *Conn {
	// 防止空slice导致panic
	if len(group.slaves) == 0 {
		return group.Conn
	}
	
	// 防止无限递归，最大重试次数限制
	maxRetries := len(group.slaves)
	if maxRetries > 10 {
		maxRetries = 10 // 硬限制最大重试10次
	}
	
	originalCursor := c.cursor
	for retries := 0; retries < maxRetries; retries++ {
		conn := group.slaves[c.cursor]
		c.cursor = (c.cursor + 1) % len(group.slaves)
		
		if conn != nil && conn.ConnIsActive() {
			c.num = 0 // 重置计数器
			return conn
		}
		
		// 如果回到起始位置，说明已经遍历了一轮
		if c.cursor == originalCursor {
			break
		}
	}
	
	// 所有从节点都不可用，返回主节点
	c.num = 0 // 重置计数器防止状态污染
	return group.Conn
}

// MinConnectPolicy 最小连接数
func MinConnectPolicy() *MinConnectPolicyHandler {
	return &MinConnectPolicyHandler{}
}

// MinConnectPolicyHandler 最小连接数
type MinConnectPolicyHandler struct {
}

func (m *MinConnectPolicyHandler) Slave(group *Group) *Conn {
	if len(group.slaves) == 0 {
		return group.Conn
	}
	
	minNum := 0
	minIndex := 0
	var isFound bool
	
	for i, slave := range group.slaves {
		// 增加安全检查
		if slave == nil {
			continue
		}
		
		if slave.ConnIsActive() {
			isFound = true
			
			// 安全获取连接统计信息
			func() {
				defer func() {
					if r := recover(); r != nil {
						log.Error("Get DB stats panic recovered", log.Any("panic", r), log.Int("slave_index", i))
					}
				}()
				
				if slave.DB != nil && slave.DB.DB() != nil {
					stats := slave.DB.DB().Stats()
					if minNum == 0 || stats.OpenConnections < minNum {
						minNum = stats.OpenConnections
						minIndex = i
					}
				}
			}()
		}
	}
	
	// 从节点全部挂掉了，返回master的connect
	if !isFound {
		return group.Conn
	}
	return group.slaves[minIndex]
}

// getGroupPolicyFuncByConf 根据配置获取对应的负载均衡策略
func getGroupPolicyFuncByConf(conf string) GroupPolicy {
	switch conf {
	case "minConnects":
		log.Info("init slave", log.String("group policy", "minConnects"))
		return MinConnectPolicy()
	case "polling":
		log.Info("init slave", log.String("group policy", "polling"))
		return CirculatePolicy()
	default:
		log.Info("init slave", log.String("group policy", "polling"))
		return CirculatePolicy()
	}
}

func getCheckPingTime(pingTime int) time.Duration {
	if pingTime <= 0 {
		return time.Duration(5)
	}
	return time.Duration(pingTime)
}
