package db

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestParseDSN(t *testing.T) {
	conf1 := ConnConf{
		DSN: "root:123456@tcp(**********:3301)/test?parseTime=true&loc=Local&charset=utf8mb4,utf8",
	}

	conf2 := ConnConf{
		DSN: "host=********** user=dstore_manager dbname=dstore_manager password=dstore_manager sslmode=disable",
	}
	_, err := ParseDSN(&conf1)
	assert.Nil(t, err)

	_, err = ParseDSN(&conf2)
	assert.Nil(t, err)
}
