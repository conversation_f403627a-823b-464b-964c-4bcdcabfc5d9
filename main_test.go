package db

//func TestMain(m *testing.M) {
//	var err error
//	if testDBMgr == nil {
//		if testDBMgr, err = NewManager(&testConf); err != nil {
//			panic(fmt.Sprintf("NewDBManager, No error should happen when connecting to test database, but got err=%+v", err))
//		}
//	}
//	if testDBConn == nil {
//		if testDBConn, err = NewConn1(&testConnConf); err != nil {
//			panic(fmt.Sprintf("NewDBConn1, No error should happen when connecting to test database, but got err=%+v", err))
//		}
//	}
//
//	testDBConn.AutoMigrate(&User{})
//	testDBConn.AutoMigrate(&Product{})
//
//	testDBConn.Create(&User{
//		Age:  18,
//		Name: "L. Jiang",
//	})
//
//	m.Run()
//}
